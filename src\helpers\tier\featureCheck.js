//this file contains the logic to check if a feature is available for a given tier

import React from 'react';
import useTeamManagementStore from '@/stores/teamManagement/teamManagementStore';
import { useUserStore } from '@/stores/user/userStore';
import useModalStore from '@/stores/modal/modalStore';

const features = {
  // "allowed_model": [],
  // "credits_per_month": ["advanced", "business", "enterprise", "entry", "premium", "pro", "professional", "starter", "ultra"],
  // "number_of_knowledgebases": ["advanced", "business", "enterprise", "entry", "free", "premium", "pro", "professional", "starter", "ultra"],
  // "characters_per_knowledgebase": ["advanced", "business", "enterprise", "entry", "premium", "pro", "professional", "starter", "ultra"],
  // "embed_unlimited_websites": ["advanced", "business", "enterprise", "entry", "free", "premium", "pro", "professional", "starter", "ultra"],
  // "upload_multiple_files": ["advanced", "business", "enterprise", "entry", "free", "premium", "pro", "professional", "starter", "ultra"],
  // "upload_videos": ["advanced", "business", "enterprise", "entry", "free", "premium", "pro", "professional", "starter", "ultra"],
  // "upload_images": ["advanced", "business", "enterprise", "entry", "free", "premium", "pro", "professional", "starter", "ultra"],
  // "save_conversations": ["advanced", "business", "enterprise", "entry", "free", "premium", "pro", "professional", "starter", "ultra"],
  'voice_to_text': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'api_access': ['starter', 'advanced', 'business', 'enterprise', 'pro', 'professional', 'ultra'],
  // "edit_knowledgebases": ["advanced", "business", "enterprise", "entry", "free", "premium", "pro", "professional", "starter", "ultra"],
  'remove_watermark': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  // "white_labelled_solution": [],
  // "external_app_integration": ["advanced", "business", "enterprise", "entry", "premium", "pro", "professional", "starter", "ultra"],
  // "dedicated_support_agent": ["business", "enterprise", "pro", "professional", "ultra"],
  // "upload_websites_feature": ["advanced", "business", "enterprise", "entry", "premium", "pro", "professional", "starter", "ultra"],
  // "chatbot_customization": ["advanced", "business", "enterprise", "entry", "premium", "pro", "professional", "starter", "ultra"],
  'disclaimer': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'sources': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'analytics_dashboard': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'advanced_chatbot_branding': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'zapier_integration': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'share_conversations': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  // "falcon_model": ["advanced", "business", "enterprise", "premium", "pro", "professional", "ultra"],
  'data_collection': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  // "client_name": ["advanced", "business", "enterprise", "entry", "premium", "pro", "professional", "starter", "ultra"],
  'whatsapp_integration': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  // "image_display": ["advanced", "business", "enterprise", "premium", "pro", "professional", "ultra"],
  'google_drive_integration': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'slack_integration': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  'teams_integration': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  'discord_integration': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  'calendly_integration': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  'dropbox_integration': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  'custom_css': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  // "premium_support": ["advanced", "business", "enterprise", "premium", "pro", "professional", "ultra"],
  'enhanced_chatbot_security': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  // "personality_customization": ["advanced", "business", "enterprise", "entry", "premium", "pro", "professional", "starter", "ultra"],
  'rate_limiting': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  // "priority_support": ["advanced", "business", "enterprise", "entry", "premium", "pro", "professional", "starter", "ultra"],
  'auto_refresh_training': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'], // TODO: add when backend is ready
  // "uptime_sla_guarantee": ["business", "enterprise", "pro", "professional", "ultra"],
  'live_agent': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  // "organisation_management": ["business", "enterprise", "pro", "professional", "ultra"],
  'unanswered_question_recognition': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  // "chat_logs": ["advanced", "business", "enterprise", "entry", "premium", "pro", "professional", "starter", "ultra"],
  'custom_url': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'cohere_model': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'user_openai_api_key': ['business', 'enterprise', 'pro', 'professional', 'ultra'],
  'intercom_integration': ['business', 'enterprise', 'pro', 'professional', 'ultra', 'advanced', 'premium'],
  // "number_of_live_agents": ["advanced", "business", "enterprise", "premium", "pro", "professional", "ultra"],
  // "number_of_live_agent_takeovers": ["advanced", "business", "enterprise", "premium", "pro", "professional", "ultra"],
  'claude_model': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  'team_management': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra', 'entry', 'starter'],
  'add_custom_roles': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra', 'entry', 'starter'],
  'realtime_voice_access': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra', 'starter', 'entry'],
  'add_initial_message': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'input_placeholder': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'dislaimer': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'show_email_details': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'show_play_button': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'show_welcome_message_as_tooltip': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'suggested_prompts': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra','free'],
  'home_tab': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'chatbot_password': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'wordpress_integration': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra', 'free'],
  'remove_powered_by_dante': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra'],
  'ai_voice_insights': ['advanced', 'business', 'enterprise', 'entry', 'premium', 'pro', 'professional', 'starter', 'ultra'],
  'buy_phone_number': ['advanced', 'business', 'enterprise', 'premium', 'pro', 'professional', 'ultra', 'entry', 'starter'],
};

// Numerical features with different values per tier
const numericalFeatures = {
  'nr_free_phone_numbers': {
    'free': 0,
    'starter': 1,
    'entry': 1,
    'advanced': 1,
    'premium': 1,
    'pro': 1,
    'professional': 1,
    'business': 1,
    'enterprise': 1,
    'ultra': 1
  },
  'nr_ai_voice_agents': {
    'free': 1,
    'starter': 5,
    'entry': 5,
    'advanced': 10,
    'premium': 10,
    'pro': 50,
    'professional': 50,
    'business': 50,
    'enterprise': 50,
    'ultra': 50
  },
  'nr_voices': {
    'free': 6,
    'starter': 50,
    'entry': 50,
    'advanced': 200,
    'premium': 200,
    'pro': 500,
    'professional': 500,
    'business': 500,
    'enterprise': 500,
    'ultra': 500
  },
  'custom_url': {
    'free': 0,
    'starter': 1,
    'entry': 1,
    'advanced': 5,
    'premium': 5,
    'pro': 50,
    'professional': 50,
    'business': 50,
    'enterprise': 50,
    'ultra': 50
  },
  'number_of_knowledgebases': {
    'free': 1,
    'starter': 5,
    'entry': 5,
    'advanced': 10,
    'premium': 10,
    'pro': 50,
    'professional': 50,
    'business': 50,
    'enterprise': 50,
    'ultra': 50
  }
};
export default function featureCheck(feature , inline = false) {
  const tier_type = useUserStore.getState().user.tier_type;
  const teamSelected = useTeamManagementStore.getState().selectedTeam?.id;

  //if team is selected, check if the feature is available for the team not the tier
  if(teamSelected) {
    const team = useUserStore.getState().user.permissions.find((permission) => permission.name === feature);
    if(team) {
      return team.value;
    }
  }

  if (features[feature] && features[feature].includes(tier_type)) {
      return true;
  }
  if(inline) {
    useModalStore.getState().openInlinePlansModal(feature);
  } else {
    useModalStore.getState().openPlansModal(feature);
  }
  return false;
}

export const checkFeatureAvailability = (feature) => {
  const tier_type = useUserStore.getState().user.tier_type;

  return features[feature] && features[feature].includes(tier_type);
}


export const checkTeamManagementPermission = (feature) => {
    const user = useUserStore.getState().user;
    const teamSelected = useTeamManagementStore.getState().selectedTeam?.id;
    const userPermissions = useUserStore.getState().user.permissions;
    if(teamSelected && teamSelected.owner_id !== user.id) {
      return userPermissions?.find((permission) => permission.name === feature)?.value;
    }
    return true;
}

export const withPermissionCheck = (WrappedComponent, requiredPermission) => {
  return function PermissionWrapper(props) {
    const hasPermission = checkTeamManagementPermission(requiredPermission);

    if (!hasPermission) {
      return null;
    }

    return React.createElement(WrappedComponent, props);
  };
};

/**
 * Gets the numerical value of a feature for the current tier
 * @param {string} feature - The feature to check
 * @returns {number} The numerical value for the feature based on the current tier, or 0 if not found
 */
export const getNumericalFeatureValue = (feature) => {
  // const tier_type = 'free'
  const tier_type = useUserStore.getState().user.tier_type;

  if (numericalFeatures[feature] && numericalFeatures[feature][tier_type] !== undefined) {
    return numericalFeatures[feature][tier_type];
  }

  return 0;
};
