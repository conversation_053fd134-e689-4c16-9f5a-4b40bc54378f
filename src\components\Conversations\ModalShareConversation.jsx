import { useEffect, useMemo, useState } from 'react';

import generateLinkShare from '@/application/conversation/generateLinkShare';
import transformLinkUri from '@/helpers/transformLinkUri';
import transformMessageToBubbleMessage from '@/helpers/transformMessageToBubbleMessage';
import useDante<PERSON><PERSON> from '@/hooks/useDanteApi';
import { getMessagesByConversationId } from '@/services/message.service';

import ChatListMessages from '../Chatbot/ChatListMessages';
import DButton from '../Global/DButton';
import DModal from '../Global/DModal';
import CopyIcon from '../Global/Icons/CopyIcon';

const ModalShareConversation = ({ open, onClose, conversation_id }) => {

  const memoizedConversationId = useMemo(
    () => conversation_id,
    [conversation_id]
  );
  const [messages, setMessages] = useState([]);
  const { data: messagesRaw, isLoading: messagesLoading } = useDante<PERSON><PERSON>(
    getMessagesByConversationId,
    [],
    {},
    memoizedConversationId
  );

  const [loadingLink, setLoadingLink] = useState(false);

  const handleCopyLink = async () => {
    try {
      setLoadingLink(true);
      const linkPathname = await generateLinkShare(conversation_id);

      const link = `${window.location.origin}${linkPathname}`;

      navigator.clipboard.writeText(link);
    } catch (error) {
      console.error(error);
    } finally {
      setLoadingLink(false);
    }
  };

  useEffect(() => {
    if (messagesRaw?.results) {
      const processedMessages = [];

      messagesRaw.results.forEach((message) => {
        const transformedMessages = transformMessageToBubbleMessage(message);
        processedMessages.push(...transformedMessages);
      });

      setMessages(processedMessages);
    }
  }, [messagesRaw]);


  return (
    <DModal
      isOpen={open}
      onClose={onClose}
      title="Share your chat with a link"
      subtitle="Messages sent after creating the link won't be shared. The link allows anyone to view this chat."
      footer={
        <DButton
          variant="dark"
          fullWidth
          onClick={handleCopyLink}
          loading={loadingLink}
        >
          <CopyIcon />
        Copy link
        </DButton>
      }    >
      <div className='h-[400px] overflow-y-auto'>
        <ChatListMessages
          transformLinkUri={transformLinkUri}
          messages={messages}
          readonly
          hideFooter
          sources={[]}
          openSources={false}
          setOpenSources={() => {}}
          showSources={[]}
          sourcesLoading={[]}
        />
      </div>
    </DModal>
  );
};

export default ModalShareConversation;
