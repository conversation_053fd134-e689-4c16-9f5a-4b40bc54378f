import {
  CloseButton,
  Dialog,
  DialogBackdrop,
  DialogPanel,
  DialogTitle,
} from '@headlessui/react';

// Import necessary icons
import ChevronLeftIcon from '../Icons/ChevronLeftIcon';
import CloseIcon from '../Icons/CloseIcon';

const DModal = ({
  children,
  title,
  subtitle,
  isOpen,
  onClose,
  footer,
  backBtn,
  onBack,
  hideCloseButton = false,
  closeBackdrop = true,
  imageSection = null,
  imageBgColor = 'bg-white', // Light blue background from reference image
  contentBgColor = 'bg-white',
  className = '',
  headerClassName = '',
  titleClassName = '',
  subtitleClassName = '',
  line = true,
}) => {
  const hasSideBySideLayout = !!imageSection;

  return (
    <>
      <Dialog
        open={isOpen}
        onClose={() => closeBackdrop && onClose()}
        className="relative z-50"
      >
        <DialogBackdrop
          transition
          className="fixed inset-0 duration-150 bg-[#000]/30 data-[closed]:opacity-0 data-[leave]:delay-100"
        />

        <div className="fixed inset-0 flex w-screen items-center justify-center">
          <DialogPanel
            transition
            className={`${className} transition-all duration-200 w-11/12 ${
              className.includes('max-w-none')
                ? ''
                : `${imageSection ? 'md:max-w-[900px]' : 'md:max-w-[550px]'}`
            } min-w-[350px] md:w-auto max-h-[90vh] rounded-size1 flex flex-col border border-grey-5 transition data-[enter]:delay-100 duration-150 data-[closed]:-translate-y-20 data-[closed]:opacity-0 overflow-hidden`}
          >
            {hasSideBySideLayout ? (
              <div className={'flex flex-col md:flex-row w-full h-full'}>
                {/* Image Section */}
                <div
                  className={`${imageBgColor} md:w-2/5 flex items-center justify-center py-size3 md:py-size8 px-size5`}
                >
                  {imageSection}
                </div>

                {/* Content Section */}
                <div
                  className={`${contentBgColor} md:w-3/5 flex flex-col gap-size3 p-size3`}
                >
                  <header
                    className={`flex items-start ${headerClassName} relative`}
                  >
                    {backBtn && (
                      <button onClick={() => onBack()} className="w-[30px]">
                        <ChevronLeftIcon />
                      </button>
                    )}
                    <div
                      className={`flex flex-col gap-size2 ${
                        title ? 'max-w-[80%]' : 'max-w-[100%]'
                      }`}
                    >
                      {title && (
                        <DialogTitle
                          className={`text-xl font-medium ${titleClassName}`}
                        >
                          {title}
                        </DialogTitle>
                      )}
                      {subtitle && (
                        <DialogTitle
                          className={`text-sm text-grey-50 w-full ${subtitleClassName}`}
                        >
                          {subtitle}
                        </DialogTitle>
                      )}
                    </div>
                    {!hideCloseButton && (
                      <CloseButton className="absolute right-0 top-[-2px]">
                        <CloseIcon width={24} height={24} />
                      </CloseButton>
                    )}
                  </header>

                  {title && line && (
                    <div className="w-full h-px bg-grey-5"></div>
                  )}
                  <main className="text-base grow overflow-y-auto overflow-x-hidden scrollbar">
                    {children}
                  </main>
                  {footer && (
                    <footer className="flex items-center gap-size1 w-full">
                      {footer}
                    </footer>
                  )}
                </div>
              </div>
            ) : (
              // Original layout when no image section is provided
              <div
                className={`${contentBgColor} flex flex-col gap-size2 p-size5 w-full`}
              >
                <header
                  className={`flex items-center ${headerClassName} relative`}
                >
                  {backBtn && (
                    <button onClick={() => onBack()} className="w-[30px]">
                      <ChevronLeftIcon />
                    </button>
                  )}
                  <div
                    className={`flex flex-col gap-size2 ${
                      title ? 'max-w-[80%]' : 'max-w-[100%]'
                    }`}
                  >
                    {title && (
                      <DialogTitle
                        className={`text-xl font-medium ${titleClassName}`}
                      >
                        {title}
                      </DialogTitle>
                    )}
                    {subtitle && (
                      <DialogTitle
                        className={`text-sm text-grey-50 w-full ${subtitleClassName}`}
                      >
                        {subtitle}
                      </DialogTitle>
                    )}
                  </div>
                  {!hideCloseButton && (
                    <CloseButton className="absolute right-0 top-0">
                      <CloseIcon width={24} height={24} />
                    </CloseButton>
                  )}
                </header>

                {title && line && <div className="w-full h-px bg-grey-5"></div>}
                <main className="text-base grow overflow-y-auto overflow-x-hidden scrollbar">
                  {children}
                </main>
                {footer && (
                  <footer className="flex items-center gap-size1 w-full">
                    {footer}
                  </footer>
                )}
              </div>
            )}
          </DialogPanel>
        </div>
      </Dialog>
    </>
  );
};
export default DModal;
